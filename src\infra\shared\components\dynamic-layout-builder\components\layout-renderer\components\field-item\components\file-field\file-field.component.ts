import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';

import { AbstractFieldComponent } from '../base/abstract-field.component';
import { FieldComponentInterface } from '../base/field-component.interface';
import { FileFieldTypes, FieldItemConfig } from '../../../../../../models/dynamic-layout-renderer.model';
import { FieldValue } from '@domain/entities/field.entity';

@Component({
  selector: 'app-file-field',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule,
    TranslateModule
  ],
  templateUrl: './file-field.component.html',
  styleUrls: ['./file-field.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class FileFieldComponent extends AbstractFieldComponent implements FieldComponentInterface {

  @Input({ required: true }) config!: FieldItemConfig;
  @Output() valueChange = new EventEmitter<{ fieldId: string; value: FieldValue }>();

  // ===== IMPLEMENT ABSTRACT METHODS =====

  /**
   * Validate rằng field type được hỗ trợ
   */
  protected validateFieldType(): void {
    const supportedTypes: FileFieldTypes[] = ['file', 'image'];
    if (!supportedTypes.includes(this.config.field.type as FileFieldTypes)) {
      console.warn(`FileFieldComponent: Unsupported field type '${this.config.field.type}'`);
    }
  }

  /**
   * Generate mock value dựa trên field type
   */
  protected override generateMockValue(): void {
    const mockData = this.mockDataService.generateMockData(this.config.field.type);
    this.mockValue.set(String(mockData));
  }

  /**
   * Lấy validators phù hợp cho field type
   */
  protected override getValidators(): any[] {
    return this.getCommonValidators();
  }

  /**
   * Lấy icon phù hợp cho field type
   */
  getFieldIcon(): string {
    switch (this.config.field.type) {
      case 'image':
        return 'image';
      default:
        return 'attach_file';
    }
  }

  // ===== IMPLEMENT INTERFACE METHODS =====

  /**
   * Handle khi field.value thay đổi từ bên ngoài
   */
  override handleFieldValueChange(): void {
    super.handleFieldValueChange();
  }

  /**
   * Lấy giá trị hiện tại của field
   */
  getFieldValue(): FieldValue {
    return this.getCurrentValue();
  }

  /**
   * Override onValueChange để emit event
   */
  override onValueChange(value: FieldValue): void {
    super.onValueChange!(value);

    // Emit change event để Form Management Service track
    const fieldId = this.config.field._id || '';
    this.valueChange.emit({ fieldId, value });
  }

  // ===== COMPONENT-SPECIFIC METHODS =====

  /**
   * Xử lý khi file được chọn
   */
  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      const file = input.files[0];
      this.onValueChange(file.name);
    }
  }

  /**
   * Lấy accepted file types
   */
  getAcceptedTypes(): string {
    if (this.config.field.type === 'image') {
      return 'image/*';
    }
    return '*/*';
  }
}
