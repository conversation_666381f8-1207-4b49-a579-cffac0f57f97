/**
 * Base Field Components Exports
 * 
 * <PERSON><PERSON>t tất cả các base components và interfaces cho field components
 * trong Dynamic Layout Builder system.
 */

// Abstract base class
export { AbstractFieldComponent } from './abstract-field.component';

// Interface definitions
export type { 
  FieldComponentInterface,
  FieldComponentType 
} from './field-component.interface';

// Utility functions
export { 
  isFieldComponent,
  getComponentTypeFromFieldType,
  FIELD_TYPE_TO_COMPONENT_TYPE 
} from './field-component.interface';
