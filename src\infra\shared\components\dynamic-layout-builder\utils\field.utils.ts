import { DateFieldTypes, Field, FieldPermissionProfile, FieldType, FileFieldTypes, NumberFieldTypes, SelectFieldTypes, TextFieldTypes } from "@domain/entities/field.entity";
import { FieldComponentType } from "@domain/entities/field.entity";
import { DEFAULT_FIELD_TYPES, FIELD_ICON_MAPS, FIELD_TYPE_LABEL_KEYS } from "../constants/field-types.const";
import { TranslateService } from "@ngx-translate/core";

/**
 * Xác định field type thuộc nhóm nào để điều phối đến component phù hợp
 */
export function getFieldComponentType(fieldType: FieldType): FieldComponentType {
  // Text-based fields
  const textTypes: TextFieldTypes[] = ['text', 'email', 'phone', 'url', 'search'];
  if (textTypes.includes(fieldType as TextFieldTypes)) {
    return 'text';
  }

  // Number-based fields
  const numberTypes: NumberFieldTypes[] = ['number', 'decimal', 'currency', 'percent'];
  if (numberTypes.includes(fieldType as NumberFieldTypes)) {
    return 'number';
  }

  // Select-based fields
  const selectTypes: SelectFieldTypes[] = ['picklist', 'multi-picklist', 'select', 'radio'];
  if (selectTypes.includes(fieldType as SelectFieldTypes)) {
    return 'select';
  }

  // Date-based fields
  const dateTypes: DateFieldTypes[] = ['date', 'datetime'];
  if (dateTypes.includes(fieldType as DateFieldTypes)) {
    return 'date';
  }

  // File-based fields
  const fileTypes: FileFieldTypes[] = ['file', 'image'];
  if (fileTypes.includes(fieldType as FileFieldTypes)) {
    return 'file';
  }

  // Specific field types
  if (fieldType === 'textarea') return 'textarea';
  if (fieldType === 'checkbox') return 'checkbox';
  if (fieldType === 'user') return 'user';

  // Unknown field type
  console.error(`FieldItemComponent: Unknown field type '${fieldType}'`);
  return 'unknown';
}


/**
 * Hàm tạo temporary ID cho field mới
 * Server sẽ dựa vào prefix 'temp-' để phân biệt field mới và field cũ
 */
export function generateTempFieldId(): string {
  const randomString = Math.random().toString(36).substring(2, 15);
  return `temp-${randomString}`;
}

export function createNewField(fieldType: FieldType, permissionProfiles: FieldPermissionProfile[], translateService: TranslateService) {
  const field: Field = DEFAULT_FIELD_TYPES.filter(f => f.type === fieldType)?.[0];

  if(field) {
    field._id = generateTempFieldId();
    field.label = getFieldTypeLabel(fieldType, translateService);
    field.permissionProfiles = permissionProfiles;
    return field;
  }
}


/**
 * Hàm lọc field types dựa trên danh sách supportedFieldTypes
 * @param supportedFieldTypes - Danh sách các FieldType được hỗ trợ
 * @returns Danh sách Field được filter
 */
export function getFilteredFieldTypes(supportedFieldTypes?: FieldType[]): Field[] {
  if (!supportedFieldTypes || supportedFieldTypes.length === 0) {
    return DEFAULT_FIELD_TYPES;
  }

  return DEFAULT_FIELD_TYPES.filter(fieldType =>
    supportedFieldTypes.includes(fieldType.type as FieldType)
  );
}

/**
 * Hàm lấy field type template dựa trên fieldType
 * @param fieldType - Type của field
 * @returns Field template hoặc null nếu không tìm thấy
 */
export function getFieldTypeById(fieldType: FieldType): Field | null {
  return DEFAULT_FIELD_TYPES.find(field => field.type === fieldType) || null;
}

export function getFieldTypeLabel(type: FieldType, translateService: TranslateService): string {
  return translateService.instant(FIELD_TYPE_LABEL_KEYS[type] || type);
}

export function getFieldIcon(fieldType: FieldType): string {
  return FIELD_ICON_MAPS[fieldType] || 'help_outline';
}